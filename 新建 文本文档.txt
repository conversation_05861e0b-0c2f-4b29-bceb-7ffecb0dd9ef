#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <sys/utsname.h>
#import <CommonCrypto/CommonDigest.h>
#import <Security/Security.h>

#pragma mark - 配置信息
// 卡密验证的APPID
static NSString *const kAppID = @"10002";
// 控制弹窗的APPID（用于后台管理）
static NSString *const kControlAppID = @"不用管";
// 在这里修改购买链接
static NSString *const kBuyURL = @"https://域名";
// API基础URL
static NSString *const kBaseURL = @"https://域名/api.php";
// 控制URL
static NSString *const kControlURL = @"https://不用管/control.php";
// UserDefaults Keys
static NSString *const kVerifiedKey = @"KamiVerified";
static NSString *const kKamiKey = @"KamiValue";
static NSString *const kExpireTimeKey = @"ExpireTime";
// Keychain Key
static NSString *const kKeychainIdentifierKey = @"com.kami.deviceid";

#pragma mark - Keychain Helper Methods
static NSString* loadDeviceIDFromKeychain(void) {
    NSMutableDictionary *query = [NSMutableDictionary dictionary];
    query[(__bridge id)kSecClass] = (__bridge id)kSecClassGenericPassword;
    query[(__bridge id)kSecAttrAccount] = kKeychainIdentifierKey;
    query[(__bridge id)kSecReturnData] = @YES;
    query[(__bridge id)kSecMatchLimit] = (__bridge id)kSecMatchLimitOne;
    
    CFDataRef result = NULL;
    OSStatus status = SecItemCopyMatching((__bridge CFDictionaryRef)query, (CFTypeRef *)&result);
    if (status == noErr && result != NULL) {
        NSData *data = (__bridge_transfer NSData *)result;
        return [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    }
    return nil;
}

static void saveDeviceIDToKeychain(NSString *deviceID) {
    NSData *data = [deviceID dataUsingEncoding:NSUTF8StringEncoding];
    
    NSMutableDictionary *query = [NSMutableDictionary dictionary];
    query[(__bridge id)kSecClass] = (__bridge id)kSecClassGenericPassword;
    query[(__bridge id)kSecAttrAccount] = kKeychainIdentifierKey;
    
    // 先尝试删除已存在的
    SecItemDelete((__bridge CFDictionaryRef)query);
    
    // 保存新的
    query[(__bridge id)kSecValueData] = data;
    SecItemAdd((__bridge CFDictionaryRef)query, NULL);
}

@interface KamiVerifyManager : NSObject
@property (nonatomic, strong) NSTimer *checkTimer;
+ (instancetype)sharedInstance;
@end

@implementation KamiVerifyManager

+ (instancetype)sharedInstance {
    static KamiVerifyManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[KamiVerifyManager alloc] init];
    });
    return instance;
}

- (NSString *)getDeviceUDID {
    // 强制清除旧的设备ID，确保使用UDID
    // 这会导致每次启动都重新获取设备ID，但可以确保使用UDID
    NSString *oldDeviceID = loadDeviceIDFromKeychain();
    if (oldDeviceID) {
        // 检查是否是UDID格式（通常是40位十六进制字符）
        if (oldDeviceID.length != 40) {
            NSLog(@"清除非UDID格式的旧设备ID: %@", oldDeviceID);
            saveDeviceIDToKeychain(nil);
        }
    }
    
    // 尝试从证书配置文件中获取UDID
    NSString *embeddedPath = [[NSBundle mainBundle] pathForResource:@"embedded" ofType:@"mobileprovision"];
    if (embeddedPath) {
        // 读取配置文件内容
        NSData *profileData = [NSData dataWithContentsOfFile:embeddedPath];
        if (profileData) {
            // 配置文件是一个特殊格式，需要提取其中的plist部分
            NSString *profileString = [[NSString alloc] initWithData:profileData encoding:NSASCIIStringEncoding];
            NSRange plistStartRange = [profileString rangeOfString:@"<?xml"];
            NSRange plistEndRange = [profileString rangeOfString:@"</plist>"];
            
            if (plistStartRange.location != NSNotFound && plistEndRange.location != NSNotFound) {
                NSRange plistRange = NSMakeRange(plistStartRange.location,
                                               plistEndRange.location + plistEndRange.length - plistStartRange.location);
                NSString *plistString = [profileString substringWithRange:plistRange];
                
                // 将plist字符串转换为数据
                NSData *plistData = [plistString dataUsingEncoding:NSASCIIStringEncoding];
                
                // 解析plist数据
                NSError *error;
                NSDictionary *plist = [NSPropertyListSerialization propertyListWithData:plistData
                                                                              options:NSPropertyListImmutable
                                                                               format:NULL
                                                                                error:&error];
                if (!error && plist) {
                    // 从配置文件中获取设备UDID列表
                    NSArray *devices = plist[@"ProvisionedDevices"];
                    if (devices && [devices isKindOfClass:[NSArray class]] && devices.count > 0) {
                        // 个人证书只会包含一个设备的UDID
                        NSString *udid = devices.firstObject;
                        NSLog(@"成功从证书中获取UDID: %@", udid);
                        
                        saveDeviceIDToKeychain(udid);
                        return udid;
                    }
                }
            }
        }
    }
    
    // 如果无法从证书获取UDID，则使用原来的方法生成设备码
    NSLog(@"无法获取UDID，使用备用方法生成设备码");
    
    // 先从 KeyChain 获取已存储的设备码
    NSString *deviceID = loadDeviceIDFromKeychain();
    if (deviceID) {
        return deviceID;
    }
    
    struct utsname systemInfo;
    uname(&systemInfo);
    NSString *deviceModel = [NSString stringWithCString:systemInfo.machine encoding:NSUTF8StringEncoding];
    
    NSNumber *totalSpace = nil;
    NSError *error = nil;
    NSDictionary *attrs = [[NSFileManager defaultManager] attributesOfFileSystemForPath:NSHomeDirectory() error:&error];
    if (!error) {
        totalSpace = [attrs objectForKey:NSFileSystemSize];
    }
    
    NSString *uniqueIdentifier = [[[UIDevice currentDevice] identifierForVendor] UUIDString];
    
    NSString *combinedString = [NSString stringWithFormat:@"%@_%@_%@",
                                uniqueIdentifier,
                                deviceModel,
                                totalSpace ?: @"unknown"];
    
    const char *cStr = [combinedString UTF8String];
    unsigned char result[CC_MD5_DIGEST_LENGTH];
    CC_MD5(cStr, (CC_LONG)strlen(cStr), result);
    
    NSMutableString *md5String = [NSMutableString stringWithCapacity:CC_MD5_DIGEST_LENGTH * 2];
    for (int i = 0; i < CC_MD5_DIGEST_LENGTH; i++) {
        [md5String appendFormat:@"%02x", result[i]];
    }
    
    saveDeviceIDToKeychain(md5String);
    return md5String;
}


- (void)getNotice:(void(^)(NSString *notice))completion {
    NSString *urlString = [NSString stringWithFormat:@"%@?api=notice&app=%@", kBaseURL, kAppID];
    NSURL *url = [NSURL URLWithString:urlString];
    
    NSURLSessionConfiguration *config = [NSURLSessionConfiguration defaultSessionConfiguration];
    config.timeoutIntervalForRequest = 5.0; // 5秒超时
    
    NSURLSession *session = [NSURLSession sessionWithConfiguration:config];
    [[session dataTaskWithURL:url completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        if (error) {
            dispatch_async(dispatch_get_main_queue(), ^{
                completion(@"请输入卡密");
            });
            return;
        }
        
        NSError *jsonError;
        NSDictionary *json = [NSJSONSerialization JSONObjectWithData:data options:0 error:&jsonError];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if (jsonError) {
                completion(@"请输入卡密");
                return;
            }
            
            if ([json[@"code"] integerValue] == 200) {
                NSString *notice = json[@"msg"][@"app_gg"];
                if (notice && ![notice isEqualToString:@""]) {
                    completion(notice);
                } else {
                    completion(@"请输入卡密");
                }
            } else {
                completion(@"请输入卡密");
            }
        });
    }] resume];
}

- (void)checkControlStatus:(void(^)(BOOL shouldShow))completion {
    NSString *urlString = [NSString stringWithFormat:@"%@?appId=%@", kControlURL, kControlAppID];
    NSURL *url = [NSURL URLWithString:urlString];
    
    NSURLSessionConfiguration *config = [NSURLSessionConfiguration defaultSessionConfiguration];
    config.timeoutIntervalForRequest = 5.0; // 5秒超时
    
    NSURLSession *session = [NSURLSession sessionWithConfiguration:config];
    [[session dataTaskWithURL:url completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        if (error) {
            dispatch_async(dispatch_get_main_queue(), ^{
                completion(YES); // 获取失败默认显示
            });
            return;
        }
        
        NSError *jsonError;
        NSDictionary *json = [NSJSONSerialization JSONObjectWithData:data options:0 error:&jsonError];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if (jsonError) {
                completion(YES); // 解析失败默认显示
                return;
            }
            
            BOOL shouldShow = [json[@"data"][@"isShow"] boolValue];
            completion(shouldShow);
        });
    }] resume];
}

- (void)checkInitialKamiStatus {
    [self checkControlStatus:^(BOOL shouldShow) {
        if (!shouldShow) {
            return;
        }
        
        NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
        NSString *savedKami = [defaults stringForKey:kKamiKey];
        
        if (!savedKami) {
            [self showVerifyAlert];
            return;
        }
        
        NSString *urlString = [NSString stringWithFormat:@"%@?api=kmlogon&app=%@&kami=%@&markcode=%@",
                              kBaseURL, kAppID, savedKami, [self getDeviceUDID]];
        NSURL *url = [NSURL URLWithString:urlString];
        
        NSURLSession *session = [NSURLSession sharedSession];
        [[session dataTaskWithURL:url completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
            if (error) {
                NSTimeInterval expireTime = [defaults doubleForKey:kExpireTimeKey];
                if (expireTime <= [[NSDate date] timeIntervalSince1970]) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        [self showVerifyAlert];
                    });
                }
                return;
            }
            
            NSError *jsonError;
            NSDictionary *json = [NSJSONSerialization JSONObjectWithData:data options:0 error:&jsonError];
            
            dispatch_async(dispatch_get_main_queue(), ^{
                if (jsonError) {
                    [self showVerifyAlert];
                    return;
                }
                
                if ([json[@"code"] integerValue] != 200) {
                    [defaults removeObjectForKey:kVerifiedKey];
                    [defaults removeObjectForKey:kKamiKey];
                    [defaults removeObjectForKey:kExpireTimeKey];
                    [defaults synchronize];
                    
                    NSString *errorMsg = json[@"msg"];
                    if (!errorMsg || [errorMsg isEqualToString:@""]) {
                        errorMsg = @"卡密已失效";
                    }
                    [self showMessage:errorMsg completion:^{
                        [self showVerifyAlert];
                    }];
                } else {
                    NSString *vipTime = json[@"msg"][@"vip"];
                    NSTimeInterval expireTime = [vipTime doubleValue];
                    
                    [defaults setDouble:expireTime forKey:kExpireTimeKey];
                    [defaults setBool:YES forKey:kVerifiedKey];
                    [defaults synchronize];
                    
                    [self startCheckTimer];
                }
            });
        }] resume];
    }];
}

- (void)verifyKami:(NSString *)kami {
    // 处理首尾空白字符
    kami = [kami stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    
    if (!kami || [kami isEqualToString:@""]) {
        [self showMessage:@"请输入卡密" completion:^{
            [self showVerifyAlert];
        }];
        return;
    }
    
    // URL编码
    NSString *encodedKami = [kami stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
    
    NSString *urlString = [NSString stringWithFormat:@"%@?api=kmlogon&app=%@&kami=%@&markcode=%@",
                          kBaseURL, kAppID, encodedKami, [self getDeviceUDID]];
    NSURL *url = [NSURL URLWithString:urlString];
    
    NSURLSession *session = [NSURLSession sharedSession];
    [[session dataTaskWithURL:url completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        if (error) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [self showMessage:@"网络错误，请重试" completion:^{
                    [self showVerifyAlert];
                }];
            });
            return;
        }
        
        NSError *jsonError;
        NSDictionary *json = [NSJSONSerialization JSONObjectWithData:data options:0 error:&jsonError];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if (jsonError) {
                [self showMessage:@"数据解析错误" completion:^{
                    [self showVerifyAlert];
                }];
                return;
            }
            
            if ([json[@"code"] integerValue] != 200) {
                NSString *errorMsg = json[@"msg"];
                if (!errorMsg || [errorMsg isEqualToString:@""]) {
                    errorMsg = @"卡密验证失败";
                }
                [self showMessage:errorMsg completion:^{
                    [self showVerifyAlert];
                }];
            } else {
                NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
                [defaults setObject:kami forKey:kKamiKey];
                
                NSString *vipTime = json[@"msg"][@"vip"];
                NSTimeInterval expireTime = [vipTime doubleValue];
                [defaults setDouble:expireTime forKey:kExpireTimeKey];
                [defaults setBool:YES forKey:kVerifiedKey];
                [defaults synchronize];
                
                // 格式化到期时间
                NSDate *expireDate = [NSDate dateWithTimeIntervalSince1970:expireTime];
                NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
                [formatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
                NSString *expireDateString = [formatter stringFromDate:expireDate];
                
                // 显示成功信息和到期时间
                [self showMessage:[NSString stringWithFormat:@"验证成功\n到期时间: %@", expireDateString] completion:^{
                    [[self topViewController] dismissViewControllerAnimated:YES completion:nil];
                }];
                
                [self startCheckTimer];
            }
        });
    }] resume];
}

- (void)showVerifyAlert {
    dispatch_async(dispatch_get_main_queue(), ^{
        // 先创建并显示弹窗，message先置空
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"卡密验证"
                                                                     message:@""
                                                              preferredStyle:UIAlertControllerStyleAlert];

        [alert addTextFieldWithConfigurationHandler:^(UITextField *textField) {
            textField.placeholder = @"请输入卡密";
        }];

        UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消"
                                                             style:UIAlertActionStyleCancel
                                                           handler:^(UIAlertAction * action) {
            // 点击取消时退出应用
            exit(0);
        }];

        UIAlertAction *verifyAction = [UIAlertAction actionWithTitle:@"验证"
                                                             style:UIAlertActionStyleDefault
                                                           handler:^(UIAlertAction * action) {
            NSString *kami = alert.textFields.firstObject.text;
            [self verifyKami:kami];
        }];

        // 先添加取消按钮（左边），再添加验证按钮（右边）
        [alert addAction:cancelAction];
        [alert addAction:verifyAction];

        [[self topViewController] presentViewController:alert animated:YES completion:^{
            // 弹窗显示后异步加载公告
            [self getNotice:^(NSString *notice) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    // 更新弹窗内容
                    alert.message = notice;
                });
            }];
        }];
    });
}

- (void)showMessage:(NSString *)message completion:(void(^)(void))completion {
    dispatch_async(dispatch_get_main_queue(), ^{
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"提示"
                                                                     message:message
                                                              preferredStyle:UIAlertControllerStyleAlert];
        
        UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"确定"
                                                         style:UIAlertActionStyleDefault
                                                       handler:^(UIAlertAction * action) {
            if (completion) {
                completion();
            }
        }];
        
        [alert addAction:okAction];
        [[self topViewController] presentViewController:alert animated:YES completion:nil];
    });
}

- (UIViewController *)topViewController {
    UIViewController *rootVC = nil;
    UIWindow *window = nil;
    
    if (@available(iOS 15.0, *)) {
        for (UIWindowScene *windowScene in [[UIApplication sharedApplication] connectedScenes]) {
            if ([windowScene isKindOfClass:[UIWindowScene class]]) {
                for (UIWindow *win in windowScene.windows) {
                    if (win.isKeyWindow) {
                        window = win;
                        break;
                    }
                }
                if (window) break;
            }
        }
    } else {
        for (UIWindow *win in [[UIApplication sharedApplication] windows]) {
            if (win.isKeyWindow) {
                window = win;
                break;
            }
        }
    }
    
    if (!window) {
        window = [[UIApplication sharedApplication] windows].firstObject;
    }
    
    rootVC = window.rootViewController;
    while (rootVC.presentedViewController) {
        rootVC = rootVC.presentedViewController;
    }
    
    return rootVC;
}

- (void)startCheckTimer {
    [self stopCheckTimer];
    
    self.checkTimer = [NSTimer scheduledTimerWithTimeInterval:300.0
                                                      target:self
                                                    selector:@selector(checkKamiStatus)
                                                    userInfo:nil
                                                     repeats:YES];
    
    [[NSRunLoop mainRunLoop] addTimer:self.checkTimer forMode:NSRunLoopCommonModes];
}

- (void)stopCheckTimer {
    [self.checkTimer invalidate];
    self.checkTimer = nil;
}

- (void)checkKamiStatus {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSString *kami = [defaults stringForKey:kKamiKey];
    if (!kami) return;
    
    NSString *urlString = [NSString stringWithFormat:@"%@?api=kmlogon&app=%@&kami=%@&markcode=%@",
                          kBaseURL, kAppID, kami, [self getDeviceUDID]];
    NSURL *url = [NSURL URLWithString:urlString];
    
    NSURLSession *session = [NSURLSession sharedSession];
    [[session dataTaskWithURL:url completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        if (error) return;
        
        NSError *jsonError;
        NSDictionary *json = [NSJSONSerialization JSONObjectWithData:data options:0 error:&jsonError];
        if (jsonError) return;
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if ([json[@"code"] integerValue] != 200) {
                [defaults removeObjectForKey:kVerifiedKey];
                [defaults removeObjectForKey:kKamiKey];
                [defaults removeObjectForKey:kExpireTimeKey];
                [defaults synchronize];
                
                [self stopCheckTimer];
                [self showVerifyAlert];
            } else {
                NSString *vipTime = json[@"msg"][@"vip"];
                NSTimeInterval expireTime = [vipTime doubleValue];
                
                [defaults setDouble:expireTime forKey:kExpireTimeKey];
                [defaults synchronize];
            }
        });
    }] resume];
}

@end

__attribute__((constructor)) static void entry(void) {
    NSLog(@"KamiVerify loaded!");
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        KamiVerifyManager *manager = [KamiVerifyManager sharedInstance];
        [manager checkInitialKamiStatus];
    });
}

